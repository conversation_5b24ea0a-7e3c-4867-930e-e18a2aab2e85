[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "ai-gen-hub"
version = "0.1.0"
description = "高性能AI服务聚合平台 - 统一多个AI供应商的接口，提供负载均衡、缓存、监控等企业级功能"
authors = ["AI Gen Hub Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "ai_gen_hub", from = "src"}]

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
pydantic = "^2.5.0"
pydantic-settings = "^2.1.0"
httpx = "^0.25.2"
redis = "^5.0.1"
aioredis = "^2.0.1"
sqlalchemy = "^2.0.23"
alembic = "^1.13.0"
asyncpg = "^0.29.0"
structlog = "^23.2.0"
prometheus-client = "^0.19.0"
openai = "^1.3.7"
google-generativeai = "^0.3.2"
anthropic = "^0.7.7"
tenacity = "^8.2.3"
circuitbreaker = "^1.4.0"
cachetools = "^5.3.2"
hashlib-compat = "^1.0.1"
python-multipart = "^0.0.6"
websockets = "^12.0"
sse-starlette = "^1.8.2"
pillow = "^10.1.0"
boto3 = "^1.34.0"
minio = "^7.2.0"
jinja2 = "^3.1.2"
psutil = "^5.9.6"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
pre-commit = "^3.6.0"
httpx = "^0.25.2"
pytest-mock = "^3.12.0"
factory-boy = "^3.3.0"

[tool.poetry.scripts]
ai-gen-hub = "ai_gen_hub.main:main"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["ai_gen_hub"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "redis.*",
    "aioredis.*",
    "circuitbreaker.*",
    "cachetools.*",
    "prometheus_client.*",
    "google.generativeai.*",
    "anthropic.*",
    "minio.*",
    "boto3.*"
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=ai_gen_hub",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
asyncio_mode = "auto"
markers = [
    "unit: 单元测试标记",
    "integration: 集成测试标记",
    "slow: 慢速测试标记",
    "external: 需要外部服务的测试标记",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[[tool.poetry.source]]
name = "aliyun"  # 镜像源名称（自定义）
url = "https://mirrors.aliyun.com/pypi/simple/"  # 阿里源地址
priority = "primary"  # 优先级设为"primary"，优先从该源下载

[[tool.poetry.source]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"  # 清华源地址
priority = "secondary"  # 次要源，当 primary 源无法访问时 fallback