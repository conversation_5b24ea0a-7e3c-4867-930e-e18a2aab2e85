# AI Gen Hub 核心依赖
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0
click>=8.0.0

# HTTP客户端
httpx>=0.25.2

# 缓存和数据库
redis>=5.0.1
aioredis>=2.0.1
sqlalchemy>=2.0.23
alembic>=1.13.0
asyncpg>=0.29.0

# 日志和监控
structlog>=23.2.0
prometheus-client>=0.19.0
psutil>=5.9.6

# AI供应商SDK
openai>=1.3.7
google-generativeai>=0.3.2
anthropic>=0.7.7

# 工具库
tenacity>=8.2.3
circuitbreaker>=1.4.0
cachetools>=5.3.2
python-multipart>=0.0.6
websockets>=12.0
sse-starlette>=1.8.2

# 图像处理
pillow>=10.1.0

# 云存储
boto3>=1.34.0
minio>=7.2.0

# 模板引擎
jinja2>=3.1.2
