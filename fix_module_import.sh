#!/bin/bash
# AI Gen Hub 模块导入问题修复脚本

set -e

echo "🔧 修复AI Gen Hub模块导入问题..."

# 进入项目目录
cd /root/workspace/git.atjog.com/aier/ai-gen-hub

# 激活虚拟环境
echo "📦 激活虚拟环境..."
source venv/bin/activate

# 安装必要的依赖
echo "📥 安装基础依赖..."
pip install --upgrade pip
pip install wheel setuptools

# 安装项目依赖
echo "📥 安装项目依赖..."
pip install click uvicorn fastapi pydantic pydantic-settings
pip install structlog aioredis httpx jinja2 psutil

# 以开发模式安装项目
echo "🔨 以开发模式安装项目..."
pip install -e .

# 验证安装
echo "✅ 验证安装..."
python -c "import ai_gen_hub; print('✅ ai_gen_hub模块导入成功')"

# 测试命令行工具
echo "🧪 测试命令行工具..."
python -m ai_gen_hub.main --help

echo "🎉 修复完成！现在可以使用以下命令启动服务："
echo "   source venv/bin/activate"
echo "   python -m ai_gen_hub.main serve"
echo "   或者："
echo "   ai-gen-hub serve"
