#!/usr/bin/env python3
"""
独立的调试页面实现

这是一个完全独立的调试页面实现，不依赖于项目的其他模块
"""

import asyncio
import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from fastapi import FastAPI, Request, HTTPException, Depends, Query
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel

# 尝试导入psutil，如果失败则提供备用实现
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

# 创建FastAPI应用
app = FastAPI(
    title="AI Gen Hub 调试仪表板",
    description="独立的调试页面实现",
    version="0.1.0"
)

# 设置模板
templates = Jinja2Templates(directory="src/ai_gen_hub/templates")

# 模拟配置
class MockSettings:
    def __init__(self):
        self.app_name = "AI Gen Hub"
        self.app_version = "0.1.0"
        self.environment = "development"
        self.debug = True
        self.api_host = "0.0.0.0"
        self.api_port = 8000

# 系统信息模型
class SystemInfo(BaseModel):
    cpu_percent: float
    memory_percent: float
    memory_total: int
    memory_available: int
    disk_percent: float
    disk_total: int
    disk_free: int
    uptime: float
    process_count: int
    timestamp: float

# 初始化应用状态
app.state.settings = MockSettings()
app.state.start_time = time.time()

def check_debug_access(request: Request) -> bool:
    """检查调试页面访问权限"""
    settings = getattr(request.app.state, "settings", None)
    if not settings:
        raise HTTPException(status_code=500, detail="配置未初始化")
    
    if settings.environment.lower() == "production":
        raise HTTPException(status_code=403, detail="调试页面在生产环境中不可用")
    
    if not settings.debug:
        raise HTTPException(status_code=403, detail="调试页面需要启用调试模式")
    
    return True

def get_system_info() -> SystemInfo:
    """获取系统信息"""
    try:
        if not PSUTIL_AVAILABLE:
            return SystemInfo(
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_total=0,
                memory_available=0,
                disk_percent=0.0,
                disk_total=0,
                disk_free=0,
                uptime=0.0,
                process_count=0,
                timestamp=time.time()
            )
        
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        boot_time = psutil.boot_time()
        uptime = time.time() - boot_time
        process_count = len(psutil.pids())
        
        return SystemInfo(
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_total=memory.total,
            memory_available=memory.available,
            disk_percent=disk.percent,
            disk_total=disk.total,
            disk_free=disk.free,
            uptime=uptime,
            process_count=process_count,
            timestamp=time.time()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")

# 路由定义
@app.get("/", response_class=HTMLResponse)
async def debug_dashboard(request: Request, _: bool = Depends(check_debug_access)):
    """调试仪表板主页"""
    settings = getattr(request.app.state, "settings", None)
    
    context = {
        "request": request,
        "title": "AI Gen Hub 调试仪表板",
        "app_name": settings.app_name if settings else "AI Gen Hub",
        "app_version": settings.app_version if settings else "Unknown",
        "environment": settings.environment if settings else "Unknown",
        "timestamp": datetime.now().isoformat(),
        "page": "dashboard"
    }
    
    return templates.TemplateResponse("debug/dashboard.html", context)

@app.get("/api/system/info")
async def get_system_status(request: Request, _: bool = Depends(check_debug_access)):
    """获取系统状态信息"""
    try:
        system_info = get_system_info()
        settings = getattr(request.app.state, "settings", None)
        
        app_info = {
            "name": settings.app_name if settings else "Unknown",
            "version": settings.app_version if settings else "Unknown",
            "environment": settings.environment if settings else "Unknown",
            "debug_mode": settings.debug if settings else False,
            "start_time": getattr(request.app.state, "start_time", time.time()),
        }
        
        # 模拟健康检查状态
        health_status = {
            "overall_status": "healthy",
            "checks": [
                {"name": "database", "status": "healthy", "message": "数据库连接正常", "duration": 0.05},
                {"name": "redis", "status": "healthy", "message": "Redis连接正常", "duration": 0.03},
                {"name": "system", "status": "healthy", "message": "系统资源正常", "duration": 0.02}
            ]
        }
        
        return {
            "system": system_info.dict(),
            "application": app_info,
            "health": health_status,
            "timestamp": time.time()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/endpoints")
async def get_endpoints(request: Request, _: bool = Depends(check_debug_access)):
    """获取所有API端点信息"""
    try:
        endpoints = []
        
        for route in request.app.routes:
            if hasattr(route, 'methods') and hasattr(route, 'path'):
                for method in route.methods:
                    if method != 'HEAD':
                        endpoint = {
                            "path": route.path,
                            "method": method,
                            "name": getattr(route, 'name', ''),
                            "description": getattr(route, 'description', ''),
                            "tags": getattr(route, 'tags', []),
                            "parameters": {}
                        }
                        endpoints.append(endpoint)
        
        endpoints = sorted(endpoints, key=lambda x: (x['path'], x['method']))
        
        return {
            "endpoints": endpoints,
            "total": len(endpoints),
            "timestamp": time.time()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/system")
async def system_monitor_page(request: Request, _: bool = Depends(check_debug_access)):
    """系统监控页面"""
    context = {
        "request": request,
        "title": "系统状态监控",
        "page": "system"
    }
    return templates.TemplateResponse("debug/system.html", context)

@app.get("/api-test")
async def api_test_page(request: Request, _: bool = Depends(check_debug_access)):
    """API测试页面"""
    context = {
        "request": request,
        "title": "API接口测试",
        "page": "api-test"
    }
    return templates.TemplateResponse("debug/api_test.html", context)

@app.get("/logs")
async def logs_page(request: Request, _: bool = Depends(check_debug_access)):
    """日志查看页面"""
    context = {
        "request": request,
        "title": "日志查看器",
        "page": "logs"
    }
    return templates.TemplateResponse("debug/logs.html", context)

@app.get("/config")
async def config_page(request: Request, _: bool = Depends(check_debug_access)):
    """配置信息页面"""
    context = {
        "request": request,
        "title": "配置信息",
        "page": "config"
    }
    return templates.TemplateResponse("debug/config.html", context)

# 添加一些测试端点
@app.get("/test")
async def test_endpoint():
    """测试端点"""
    return {"message": "这是一个测试端点", "timestamp": time.time()}

@app.post("/echo")
async def echo_endpoint(data: dict):
    """回显端点"""
    return {"echo": data, "timestamp": time.time()}

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动AI Gen Hub调试页面...")
    print("📍 访问 http://localhost:8000/ 查看调试仪表板")
    print("📍 访问 http://localhost:8000/system 查看系统监控")
    print("📍 访问 http://localhost:8000/api-test 查看API测试")
    uvicorn.run(app, host="0.0.0.0", port=8000)
